name: ✨ New feature

description: Propose a new feature to be added to Nova-admin

body:
  - type: markdown
    attributes:
      value: |
        Thanks for your interest in the project and taking the time to fill out this feature report!

  - type: textarea
    id: feature-description
    attributes:
      label: Description
      description: Clear and concise description of the problem. Please make the reason and usecases as detailed as possible. If you intend to submit a PR for this issue, tell us in the description. Thanks!
      placeholder: As a developer using Nova-admin I want [goal / wish] so that [benefit]...
    validations:
      required: true

  - type: textarea
    id: suggested-solution
    attributes:
      label: Suggestion
      description: In module [xy] we could provide following implementation...
    validations:
      required: true

  - type: textarea
    id: additional-context
    attributes:
      label: Additional context
      description: Any other context or screenshots about the feature request here.

  - type: checkboxes
    id: checkboxes
    attributes:
      label: Validations
      description: Before submitting the issue, please make sure you do the following
      options:
        - label: Ensure this issue not a feature proposal.
          required: true
        - label: Read the [docs](https://nova-admin-docs.pages.dev/).
          required: true
        - label: Check that there isn't [already an issue](https://github.com/chansee97/nova-admin/issues) that descript the same thing to avoid creating a duplicate.
          required: true
