{"name": "nova-admin", "type": "module", "version": "0.9.18", "private": true, "description": "a clean and concise back-end management template based on Vue3, Vite5, Typescript, and Naive UI.", "author": {"name": "chansee97", "email": "<EMAIL>", "url": "https://github.com/chansee97"}, "license": "MIT", "homepage": "https://github.com/chansee97/nova-admin", "repository": {"url": "https://github.com/chansee97/nova-admin.git"}, "bugs": {"url": "https://github.com/chansee97/nova-admin/issues"}, "keywords": ["<PERSON><PERSON>", "Vue3", "admin", "admin-template", "vue-admin", "vue-admin-template", "Vite5", "Vite", "vite-admin", "TypeScript", "TS", "NaiveUI", "naive-ui", "naive-admin", "NaiveUI-Admin", "naive-ui-admin", "UnoCSS"], "scripts": {"dev": "vite --mode dev --port 9980", "dev:prod": "vite --mode prod", "build": "vite build --mode prod", "build:dev": "vite build --mode dev", "preview": "vite preview --port 9981", "lint": "eslint . && vue-tsc --noEmit", "lint:fix": "eslint . --fix", "lint:check": "npx @eslint/config-inspector", "sizecheck": "npx vite-bundle-visualizer"}, "dependencies": {"@vueuse/core": "^13.6.0", "alova": "^3.3.4", "colord": "^2.9.3", "echarts": "^5.6.0", "md-editor-v3": "^5.6.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "pro-naive-ui": "^2.4.3", "quill": "^2.0.3", "radash": "^12.1.1", "vue": "^3.5.18", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^5.0.0", "@iconify-json/icon-park-outline": "^1.2.2", "@iconify/vue": "^5.0.0", "@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "eslint": "^9.29.0", "lint-staged": "^16.1.2", "naive-ui": "^2.42.0", "sass": "^1.89.2", "simple-git-hooks": "^2.13.1", "typescript": "^5.8.3", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.2.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.6", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "8.0.0", "vue-tsc": "^3.0.5"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}