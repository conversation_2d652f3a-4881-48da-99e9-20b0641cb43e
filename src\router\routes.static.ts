export const staticRoutes: AppRoute.RowRoute[] = [
  {
    name: 'dashboard',
    path: '/dashboard',
    title: '仪表盘',
    requiresAuth: true,
    icon: 'icon-park-outline:analysis',
    menuType: 'dir',
    componentPath: null,
    id: 1,
    pid: null,
  },
  {
    name: 'workbench',
    path: '/dashboard/workbench',
    title: '工作台',
    requiresAuth: true,
    icon: 'icon-park-outline:alarm',
    pinTab: true,
    menuType: 'page',
    componentPath: '/dashboard/workbench/index.vue',
    id: 101,
    pid: 1,
  },
  {
    name: 'monitor',
    path: '/dashboard/monitor',
    title: '监控页',
    requiresAuth: true,
    icon: 'icon-park-outline:anchor',
    menuType: 'page',
    componentPath: '/dashboard/monitor/index.vue',
    id: 102,
    pid: 1,
  },
  {
    name: 'multi',
    path: '/multi',
    title: '多级菜单演示',
    requiresAuth: true,
    icon: 'icon-park-outline:list',
    menuType: 'dir',
    componentPath: null,
    id: 2,
    pid: null,
  },
  {
    name: 'multi2',
    path: '/multi/multi-2',
    title: '多级菜单子页',
    requiresAuth: true,
    icon: 'icon-park-outline:list',
    menuType: 'page',
    componentPath: '/demo/multi/multi-2/index.vue',
    id: 201,
    pid: 2,
  },
  {
    name: 'multi2-detail',
    path: '/multi/multi-2/detail',
    title: '菜单详情页',
    requiresAuth: false,
    icon: 'icon-park-outline:list',
    hide: true,
    activeMenu: '/multi/multi-2',
    menuType: 'page',
    componentPath: '/demo/multi/multi-2/detail/index.vue',
    id: 20101,
    pid: 2,
  },
  {
    name: 'multi3',
    path: '/multi/multi-3',
    title: '多级菜单',
    requiresAuth: true,
    icon: 'icon-park-outline:list',
    menuType: 'dir',
    componentPath: null,
    id: 202,
    pid: 2,
  },
  {
    name: 'multi4',
    path: '/multi/multi-3/multi-4',
    title: '多级菜单3-1',
    requiresAuth: true,
    icon: 'icon-park-outline:list',
    componentPath: '/demo/multi/multi-3/multi-4/index.vue',
    id: 20201,
    pid: 202,
  },
  {
    name: 'list',
    path: '/list',
    title: '列表页',
    requiresAuth: true,
    icon: 'icon-park-outline:list-two',
    menuType: 'dir',
    componentPath: null,
    id: 3,
    pid: null,
  },
  {
    name: 'commonList',
    path: '/list/common-list',
    title: '常用列表',
    requiresAuth: true,
    icon: 'icon-park-outline:list-view',
    componentPath: '/demo/list/common-list/index.vue',
    id: 301,
    pid: 3,
  },
  {
    name: 'cardList',
    path: '/list/card-list',
    title: '卡片列表',
    requiresAuth: true,
    icon: 'icon-park-outline:view-grid-list',
    componentPath: '/demo/list/card-list/index.vue',
    id: 302,
    pid: 3,
  },
  {
    name: 'draggableList',
    path: '/list/draggable-list',
    title: '拖拽列表',
    requiresAuth: true,
    icon: 'icon-park-outline:menu-fold',
    componentPath: '/demo/list/draggable-list/index.vue',
    id: 303,
    pid: 3,
  },
  {
    name: 'demo',
    path: '/demo',
    title: '功能示例',
    requiresAuth: true,
    icon: 'icon-park-outline:application-one',
    menuType: 'dir',
    componentPath: null,
    id: 4,
    pid: null,
  },
  {
    name: 'fetch',
    path: '/demo/fetch',
    title: '请求示例',
    requiresAuth: true,
    icon: 'icon-park-outline:international',
    componentPath: '/demo/fetch/index.vue',
    id: 401,
    pid: 4,
  },
  {
    name: 'echarts',
    path: '/demo/echarts',
    title: 'ECharts',
    requiresAuth: true,
    icon: 'icon-park-outline:chart-proportion',
    componentPath: '/demo/echarts/index.vue',
    id: 402,
    pid: 4,
  },
  {
    name: 'map',
    path: '/demo/map',
    title: '地图',
    requiresAuth: true,
    icon: 'carbon:map',
    keepAlive: true,
    componentPath: '/demo/map/index.vue',
    id: 403,
    pid: 4,
  },
  {
    name: 'editor',
    path: '/demo/editor',
    title: '编辑器',
    requiresAuth: true,
    icon: 'icon-park-outline:editor',
    menuType: 'dir',
    componentPath: null,
    id: 404,
    pid: 4,
  },
  {
    name: 'editorMd',
    path: '/demo/editor/md',
    title: 'MarkDown',
    requiresAuth: true,
    icon: 'ri:markdown-line',
    componentPath: '/demo/editor/md/index.vue',
    id: 40401,
    pid: 404,
  },
  {
    name: 'editorRich',
    path: '/demo/editor/rich',
    title: '富文本',
    requiresAuth: true,
    icon: 'icon-park-outline:edit-one',
    componentPath: '/demo/editor/rich/index.vue',
    id: 40402,
    pid: 404,
  },
  {
    name: 'clipboard',
    path: '/demo/clipboard',
    title: '剪贴板',
    requiresAuth: true,
    icon: 'icon-park-outline:clipboard',
    componentPath: '/demo/clipboard/index.vue',
    id: 405,
    pid: 4,
  },
  {
    name: 'icons',
    path: '/demo/icons',
    title: '图标',
    requiresAuth: true,
    icon: 'local:cool',
    componentPath: '/demo/icons/index.vue',
    id: 406,
    pid: 4,
  },
  {
    name: 'QRCode',
    path: '/demo/qr-code',
    title: '二维码',
    requiresAuth: true,
    icon: 'icon-park-outline:two-dimensional-code',
    componentPath: '/demo/qr-code/index.vue',
    id: 407,
    pid: 4,
  },
  {
    name: 'cascader',
    path: '/demo/cascader',
    title: '省市区联动',
    requiresAuth: true,
    icon: 'icon-park-outline:add-subset',
    componentPath: '/demo/cascader/index.vue',
    id: 408,
    pid: 4,
  },
  {
    name: 'dict',
    path: '/demo/dict',
    title: '字典示例',
    requiresAuth: true,
    icon: 'icon-park-outline:book-one',
    componentPath: '/demo/dict/index.vue',
    id: 409,
    pid: 4,
  },
  {
    name: 'documents',
    path: '/documents',
    title: '外链文档',
    requiresAuth: true,
    icon: 'icon-park-outline:file-doc',
    menuType: 'dir',
    componentPath: null,
    id: 5,
    pid: null,
  },
  {
    name: 'documentsVue',
    path: '/documents/vue',
    title: 'Vue',
    requiresAuth: true,
    icon: 'logos:vue',
    componentPath: '/demo/documents/vue/index.vue',
    id: 501,
    pid: 5,
  },
  {
    name: 'documentsVite',
    path: '/documents/vite',
    title: 'Vite',
    requiresAuth: true,
    icon: 'logos:vitejs',
    componentPath: '/demo/documents/vite/index.vue',
    id: 502,
    pid: 5,
  },
  {
    name: 'documentsVueuse',
    path: '/documents/vue-use',
    title: 'VueUse（外链）',
    requiresAuth: true,
    icon: 'logos:vueuse',
    href: 'https://vueuse.org/guide/',
    componentPath: 'null',
    id: 503,
    pid: 5,
  },

  {
    name: 'documentsNova',
    path: '/documents/nova',
    title: 'Nova docs',
    requiresAuth: true,
    icon: 'local:logo',
    href: 'https://nova-admin-docs.netlify.app/',
    componentPath: '2333333',
    id: 504,
    pid: 5,
  },
  {
    name: 'documentsPublic',
    path: '/documents/public',
    title: '公共示例页（外链）',
    requiresAuth: true,
    icon: 'local:logo',
    href: '/public',
    componentPath: 'null',
    id: 505,
    pid: 5,
  },
  {
    name: 'permission',
    path: '/permission',
    title: '权限',
    requiresAuth: true,
    icon: 'icon-park-outline:people-safe',
    menuType: 'dir',
    componentPath: null,
    id: 6,
    pid: null,
  },
  {
    name: 'permissionDemo',
    path: '/permission/permission',
    title: '权限示例',
    requiresAuth: true,
    icon: 'icon-park-outline:right-user',
    componentPath: '/demo/permission/permission/index.vue',
    id: 601,
    pid: 6,
  },
  {
    name: 'justSuper',
    path: '/permission/just-super',
    title: 'super可见',
    requiresAuth: true,
    roles: [
      'super',
    ],
    icon: 'icon-park-outline:wrong-user',
    componentPath: '/demo/permission/just-super/index.vue',
    id: 602,
    pid: 6,
  },
  {
    name: 'setting',
    path: '/setting',
    title: '系统设置',
    requiresAuth: true,
    icon: 'icon-park-outline:setting',
    menuType: 'dir',
    componentPath: null,
    id: 7,
    pid: null,
  },
  {
    name: 'accountSetting',
    path: '/setting/account',
    title: '用户设置',
    requiresAuth: true,
    icon: 'icon-park-outline:every-user',
    componentPath: '/setting/account/index.vue',
    id: 701,
    pid: 7,
  },
  {
    name: 'dictionarySetting',
    path: '/setting/dictionary',
    title: '字典设置',
    requiresAuth: true,
    icon: 'icon-park-outline:book-one',
    componentPath: '/setting/dictionary/index.vue',
    id: 702,
    pid: 7,
  },
  {
    name: 'menuSetting',
    path: '/setting/menu',
    title: '菜单设置',
    requiresAuth: true,
    icon: 'icon-park-outline:application-menu',
    componentPath: '/setting/menu/index.vue',
    id: 703,
    pid: 7,
  },
  {
    name: 'about',
    path: '/about',
    title: '关于',
    requiresAuth: true,
    icon: 'icon-park-outline:info',
    componentPath: '/demo/about/index.vue',
    id: 8,
    pid: null,
  },
  {
    name: 'userCenter',
    path: '/user-center',
    title: '个人中心',
    requiresAuth: true,
    hide: true,
    icon: 'carbon:user-avatar-filled-alt',
    componentPath: '/build-in/user-center/index.vue',
    id: 999,
    pid: null,
  },

]
